/**
 * English Assessment Results Modal
 * Professional modal for displaying detailed English assessment analysis
 * Matches skills gap modal dimensions and styling
 */

(function() {
    'use strict';

    let isModalInitialized = false;
    let currentEnglishData = null;

    // Public API
    window.EnglishResultsModal = {
        show: showEnglishResultsModal,
        hide: hideModal
    };

    /**
     * Show English assessment results modal
     * @param {Object} englishData - English assessment data
     * @param {string} userEmail - User's email
     * @param {string} userName - User's name
     * @param {string} userCompany - Company ID
     */
    async function showEnglishResultsModal(englishData, userEmail, userName, userCompany) {
        try {
            // Show loading overlay immediately
            if (typeof showLoadingOverlay === 'function') {
                showLoadingOverlay();
            }

            // Fetch enhanced English assessment data
            const enhancedData = await fetchEnhancedEnglishData(userEmail, userCompany);
            currentEnglishData = enhancedData || englishData;

            if (isModalInitialized) {
                await resetAndShowModal(userName);
                return;
            }

            // Create modal if it doesn't exist
            await createModal(userName);
            isModalInitialized = true;

        } catch (error) {
            console.error('Error showing English results modal:', error);
            if (typeof hideLoadingOverlay === 'function') {
                hideLoadingOverlay();
            }
            showErrorMessage('Failed to load English assessment results');
        }
    }

    /**
     * Fetch enhanced English assessment data with detailed analysis
     */
    async function fetchEnhancedEnglishData(userEmail, userCompany) {
        try {
            if (typeof db === 'undefined') {
                throw new Error('Database not initialized');
            }

            const userRef = db.collection('companies')
                             .doc(userCompany)
                             .collection('users')
                             .doc(userEmail);

            const userDoc = await userRef.get();
            if (!userDoc.exists) {
                throw new Error('User document not found');
            }

            const userData = userDoc.data();
            
            // Build comprehensive English assessment data
            const englishData = {
                // Core assessment fields
                englishAssessmentCompleted: userData.englishAssessmentCompleted || false,
                englishProficiencyScore: userData.englishProficiencyScore || userData.englishAssessment?.totalPoints || 0,
                englishProficiencyLevel: userData.englishProficiencyLevel || userData.englishAssessment?.level || 'Entry',
                englishResponse: userData.englishResponse || '',
                englishAssessmentTimestamp: userData.englishAssessmentTimestamp || userData.englishAssessment?.timestamp || null,
                timeSpentOnEnglish: userData.timeSpentOnEnglish || 0,
                
                // Enhanced analysis fields with fallbacks
                englishFeedback: userData.englishFeedback || getDefaultFeedback(userData.englishProficiencyScore || userData.englishAssessment?.totalPoints || 0),
                englishStrengths: userData.englishStrengths || getDefaultStrengths(userData.englishProficiencyScore || userData.englishAssessment?.totalPoints || 0),
                englishImprovements: userData.englishImprovements || getDefaultImprovements(userData.englishProficiencyScore || userData.englishAssessment?.totalPoints || 0),
                
                // Legacy support
                totalPoints: userData.englishAssessment?.totalPoints || userData.englishProficiencyScore || 0,
                level: userData.englishAssessment?.level || userData.englishProficiencyLevel || 'Entry',
                completed: userData.englishAssessmentCompleted || false
            };

            return englishData;

        } catch (error) {
            console.error('Error fetching enhanced English data:', error);
            return null;
        }
    }

    /**
     * Generate default feedback based on score
     */
    function getDefaultFeedback(score) {
        if (score >= 16) {
            return {
                grammar: 'Excellent grammar skills demonstrated with complex sentence structures and accurate usage.',
                vocabulary: 'Strong vocabulary range with appropriate word choice and professional terminology.',
                coherence: 'Well-organized responses with clear logical flow and effective transitions.',
                overall: 'Outstanding English proficiency at L2/GCSE level. Ready for advanced digital skills training.'
            };
        } else if (score >= 10) {
            return {
                grammar: 'Good grammar foundation with mostly accurate sentence construction.',
                vocabulary: 'Adequate vocabulary for everyday communication with room for expansion.',
                coherence: 'Generally well-structured responses with clear main ideas.',
                overall: 'Solid English proficiency at L1 level. Some areas for improvement before advanced training.'
            };
        } else {
            return {
                grammar: 'Basic grammar understanding with opportunities for improvement in sentence structure.',
                vocabulary: 'Foundational vocabulary that would benefit from expansion and development.',
                coherence: 'Simple but understandable communication with potential for better organization.',
                overall: 'Entry level English proficiency. Focused language development recommended.'
            };
        }
    }

    /**
     * Generate default strengths based on score
     */
    function getDefaultStrengths(score) {
        if (score >= 16) {
            return [
                'Excellent written communication skills',
                'Strong grammar and sentence structure',
                'Professional vocabulary usage',
                'Clear and coherent expression',
                'Ready for advanced learning'
            ];
        } else if (score >= 10) {
            return [
                'Good basic communication skills',
                'Adequate grammar foundation',
                'Clear expression of ideas',
                'Willingness to engage with assessment'
            ];
        } else {
            return [
                'Completed the assessment',
                'Basic communication attempted',
                'Foundation for improvement established'
            ];
        }
    }

    /**
     * Generate default improvements based on score
     */
    function getDefaultImprovements(score) {
        if (score >= 16) {
            return [
                'Continue practicing advanced writing techniques',
                'Expand professional vocabulary',
                'Maintain current proficiency level'
            ];
        } else if (score >= 10) {
            return [
                'Practice complex sentence structures',
                'Expand vocabulary range',
                'Work on coherence and organization',
                'Consider additional English language support'
            ];
        } else {
            return [
                'Focus on basic grammar rules',
                'Build foundational vocabulary',
                'Practice sentence construction',
                'Consider English language courses',
                'Regular reading and writing practice'
            ];
        }
    }

    /**
     * Create and show the modal
     */
    async function createModal(userName) {
        // Remove existing modal if any
        const existingOverlay = document.getElementById('english-results-overlay');
        if (existingOverlay) {
            existingOverlay.remove();
        }

        // Create modal HTML
        const modalHTML = createModalHTML(userName);

        // Add to DOM
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Add styles if not already added
        addModalStyles();

        // Initialize event listeners
        const overlay = document.getElementById('english-results-overlay');
        initializeEventListeners(overlay);

        // Show modal with animation
        overlay.style.display = 'flex';
        overlay.style.opacity = '0';

        // Hide loading overlay
        if (typeof hideLoadingOverlay === 'function') {
            hideLoadingOverlay();
        }

        // Animate in
        requestAnimationFrame(() => {
            overlay.style.opacity = '1';
        });
    }

    /**
     * Reset and show existing modal
     */
    async function resetAndShowModal(userName) {
        const overlay = document.getElementById('english-results-overlay');
        if (!overlay) return;

        // Rebuild modal HTML with new data
        overlay.innerHTML = createModalContent(userName);

        // Re-initialize event listeners
        initializeEventListeners(overlay);

        // Show modal with animation
        overlay.style.display = 'flex';
        overlay.style.opacity = '0';

        // Hide loading overlay
        if (typeof hideLoadingOverlay === 'function') {
            hideLoadingOverlay();
        }

        // Animate in
        requestAnimationFrame(() => {
            overlay.style.opacity = '1';
        });
    }

    /**
     * Create modal HTML structure
     */
    function createModalHTML(userName) {
        return `
            <div id="english-results-overlay" class="english-modal-overlay">
                ${createModalContent(userName)}
            </div>
        `;
    }

    /**
     * Create modal content
     */
    function createModalContent(userName) {
        if (!currentEnglishData) {
            return createErrorContent();
        }

        const isQualified = currentEnglishData.englishProficiencyScore >= 16;
        const score = currentEnglishData.englishProficiencyScore || currentEnglishData.totalPoints || 0;
        const level = currentEnglishData.englishProficiencyLevel || currentEnglishData.level || 'Entry';
        const feedback = currentEnglishData.englishFeedback || {};
        const strengths = currentEnglishData.englishStrengths || [];
        const improvements = currentEnglishData.englishImprovements || [];
        const timeSpent = currentEnglishData.timeSpentOnEnglish || 0;
        const timestamp = currentEnglishData.englishAssessmentTimestamp || currentEnglishData.timestamp;

        // Format timestamp
        const formattedDate = timestamp ? new Date(timestamp.toDate ? timestamp.toDate() : timestamp).toLocaleDateString('en-GB', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        }) : 'Date not available';

        // Format time spent
        const formattedTime = timeSpent > 0 ? `${Math.round(timeSpent / 60)} minutes` : 'Time not recorded';

        return `
            <div class="english-modal-content">
                <div class="english-modal-header">
                    <div class="english-modal-title-container">
                        <h2 class="english-modal-employee-title">${userName}</h2>
                        <h3 class="english-modal-subtitle">English Assessment Results</h3>
                    </div>
                    <div class="english-modal-actions">
                        <button id="close-english-modal" class="english-close-modal-button">
                            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="english-modal-body">
                    <!-- Score Overview Section -->
                    <div class="english-score-overview ${isQualified ? 'qualified' : 'needs-improvement'}">
                        <div class="english-score-display">
                            <div class="english-score-value">${score}/21</div>
                            <div class="english-score-level">${level} Level</div>
                            <div class="english-score-status">
                                ${isQualified ? '✅ Qualified for Digital Skills Training' : '📚 Additional English Support Recommended'}
                            </div>
                        </div>
                        <div class="english-assessment-meta">
                            <div class="english-meta-item">
                                <span class="english-meta-label">Completed:</span>
                                <span class="english-meta-value">${formattedDate}</span>
                            </div>
                            <div class="english-meta-item">
                                <span class="english-meta-label">Time Spent:</span>
                                <span class="english-meta-value">${formattedTime}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Analysis Section -->
                    <div class="english-analysis-section">
                        <h3>Performance Analysis</h3>
                        <div class="english-feedback-grid">
                            <div class="english-feedback-item">
                                <h4>Grammar & Structure</h4>
                                <p>${feedback.grammar || 'Assessment completed'}</p>
                            </div>
                            <div class="english-feedback-item">
                                <h4>Vocabulary & Usage</h4>
                                <p>${feedback.vocabulary || 'Vocabulary evaluated'}</p>
                            </div>
                            <div class="english-feedback-item">
                                <h4>Organization & Coherence</h4>
                                <p>${feedback.coherence || 'Structure assessed'}</p>
                            </div>
                        </div>
                        <div class="english-overall-feedback">
                            <h4>Overall Assessment</h4>
                            <p>${feedback.overall || 'Assessment completed successfully'}</p>
                        </div>
                    </div>

                    <!-- Strengths and Improvements Section -->
                    <div class="english-strengths-improvements">
                        <div class="english-strengths-section">
                            <h4>Identified Strengths</h4>
                            <ul class="english-strengths-list">
                                ${strengths.map(strength => `<li>${strength}</li>`).join('')}
                            </ul>
                        </div>
                        <div class="english-improvements-section">
                            <h4>Areas for Development</h4>
                            <ul class="english-improvements-list">
                                ${improvements.map(improvement => `<li>${improvement}</li>`).join('')}
                            </ul>
                        </div>
                    </div>

                    <!-- Next Steps Section -->
                    <div class="english-next-steps">
                        <h4>Recommended Next Steps</h4>
                        <p class="english-next-steps-text">
                            ${getNextStepsMessage(isQualified, score)}
                        </p>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Create error content when data is not available
     */
    function createErrorContent() {
        return `
            <div class="english-modal-content">
                <div class="english-modal-header">
                    <div class="english-modal-title-container">
                        <h2 class="english-modal-employee-title">English Assessment Results</h2>
                        <h3 class="english-modal-subtitle">Data Not Available</h3>
                    </div>
                    <div class="english-modal-actions">
                        <button id="close-english-modal" class="english-close-modal-button">
                            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="english-modal-body">
                    <div class="english-error-state">
                        <div class="english-error-icon">⚠️</div>
                        <h3>Unable to Load Assessment Data</h3>
                        <p>We're having trouble loading the English assessment information for this user.</p>
                        <button onclick="location.reload()" class="english-retry-button">
                            Refresh Page
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Get next steps message based on qualification status
     */
    function getNextStepsMessage(isQualified, score) {
        if (isQualified) {
            return 'Excellent work! This learner has achieved the required English proficiency level (L2/GCSE) and is ready to proceed with digital skills assessment and training programs.';
        } else if (score >= 10) {
            return 'This learner has demonstrated good foundational English skills at L1 level. With some additional support and practice, they should be able to reach the qualification threshold for digital skills training.';
        } else {
            return 'This learner would benefit from focused English language development before proceeding with digital skills training. Consider recommending English language courses or additional support resources.';
        }
    }

    /**
     * Initialize event listeners for the modal
     */
    function initializeEventListeners(overlay) {
        const closeButton = overlay.querySelector('#close-english-modal');
        if (closeButton) {
            closeButton.addEventListener('click', hideModal);
        }

        overlay.addEventListener('click', overlayClickHandler);
    }

    /**
     * Handle overlay click to close modal
     */
    function overlayClickHandler(event) {
        if (event.target.id === 'english-results-overlay') {
            hideModal();
        }
    }

    /**
     * Hide the modal
     */
    function hideModal() {
        const overlay = document.getElementById('english-results-overlay');
        if (overlay) {
            overlay.style.opacity = '0';
            setTimeout(() => {
                overlay.style.display = 'none';
            }, 300);
        }
    }

    /**
     * Show error message
     */
    function showErrorMessage(message) {
        if (typeof showNotification === 'function') {
            showNotification(message, 'error');
        } else {
            alert(message);
        }
    }

    /**
     * Add modal styles to the page
     */
    function addModalStyles() {
        // Check if styles already exist
        if (document.getElementById('english-results-modal-styles')) {
            return;
        }

        const styleSheet = document.createElement('style');
        styleSheet.id = 'english-results-modal-styles';
        styleSheet.textContent = `
            /* English Results Modal Styles - Matches Skills Gap Modal Dimensions */
            .english-modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            .english-modal-content {
                background: white;
                border-radius: 12px;
                width: 90%;
                max-width: 900px;
                max-height: 90vh;
                overflow-y: auto;
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
                position: relative;
            }

            .english-modal-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                padding: 24px 24px 0 24px;
                border-bottom: 1px solid #e5e7eb;
                margin-bottom: 24px;
            }

            .english-modal-title-container h2 {
                font-size: 1.5rem;
                font-weight: 600;
                color: #111827;
                margin: 0 0 4px 0;
            }

            .english-modal-title-container h3 {
                font-size: 1rem;
                font-weight: 500;
                color: #6b7280;
                margin: 0;
            }

            .english-close-modal-button {
                background: none;
                border: none;
                color: #6b7280;
                cursor: pointer;
                padding: 8px;
                border-radius: 6px;
                transition: all 0.2s ease;
            }

            .english-close-modal-button:hover {
                background-color: #f3f4f6;
                color: #374151;
            }

            .english-modal-body {
                padding: 0 24px 24px 24px;
            }

            /* Score Overview Section */
            .english-score-overview {
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                border-radius: 12px;
                padding: 24px;
                margin-bottom: 24px;
                border-left: 4px solid #3b82f6;
            }

            .english-score-overview.qualified {
                background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
                border-left-color: #10b981;
            }

            .english-score-overview.needs-improvement {
                background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
                border-left-color: #f59e0b;
            }

            .english-score-display {
                text-align: center;
                margin-bottom: 16px;
            }

            .english-score-value {
                font-size: 3rem;
                font-weight: 700;
                color: #111827;
                line-height: 1;
            }

            .english-score-level {
                font-size: 1.25rem;
                font-weight: 600;
                color: #6b7280;
                margin: 8px 0;
            }

            .english-score-status {
                font-size: 1rem;
                font-weight: 500;
                padding: 8px 16px;
                border-radius: 20px;
                display: inline-block;
                background: rgba(255, 255, 255, 0.8);
                color: #374151;
            }

            .english-assessment-meta {
                display: flex;
                justify-content: center;
                gap: 32px;
                margin-top: 16px;
            }

            .english-meta-item {
                text-align: center;
            }

            .english-meta-label {
                display: block;
                font-size: 0.875rem;
                color: #6b7280;
                margin-bottom: 4px;
            }

            .english-meta-value {
                font-weight: 600;
                color: #111827;
            }

            /* Analysis Section */
            .english-analysis-section {
                margin-bottom: 24px;
            }

            .english-analysis-section h3 {
                font-size: 1.25rem;
                font-weight: 600;
                color: #111827;
                margin-bottom: 16px;
            }

            .english-feedback-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 16px;
                margin-bottom: 20px;
            }

            .english-feedback-item {
                background: #f8fafc;
                border-radius: 8px;
                padding: 16px;
                border: 1px solid #e2e8f0;
            }

            .english-feedback-item h4 {
                font-size: 1rem;
                font-weight: 600;
                color: #374151;
                margin: 0 0 8px 0;
            }

            .english-feedback-item p {
                font-size: 0.875rem;
                color: #6b7280;
                margin: 0;
                line-height: 1.5;
            }

            .english-overall-feedback {
                background: #f1f5f9;
                border-radius: 8px;
                padding: 16px;
                border-left: 4px solid #3b82f6;
            }

            .english-overall-feedback h4 {
                font-size: 1rem;
                font-weight: 600;
                color: #1e293b;
                margin: 0 0 8px 0;
            }

            .english-overall-feedback p {
                font-size: 0.875rem;
                color: #475569;
                margin: 0;
                line-height: 1.5;
            }

            /* Strengths and Improvements */
            .english-strengths-improvements {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 24px;
                margin-bottom: 24px;
            }

            @media (max-width: 768px) {
                .english-strengths-improvements {
                    grid-template-columns: 1fr;
                }
            }

            .english-strengths-section,
            .english-improvements-section {
                background: #fafafa;
                border-radius: 8px;
                padding: 20px;
            }

            .english-strengths-section h4,
            .english-improvements-section h4 {
                font-size: 1rem;
                font-weight: 600;
                color: #111827;
                margin: 0 0 12px 0;
            }

            .english-strengths-list,
            .english-improvements-list {
                list-style: none;
                padding: 0;
                margin: 0;
            }

            .english-strengths-list li,
            .english-improvements-list li {
                padding: 6px 0;
                font-size: 0.875rem;
                color: #374151;
                position: relative;
                padding-left: 20px;
            }

            .english-strengths-list li:before {
                content: "✓";
                position: absolute;
                left: 0;
                color: #10b981;
                font-weight: bold;
            }

            .english-improvements-list li:before {
                content: "→";
                position: absolute;
                left: 0;
                color: #f59e0b;
                font-weight: bold;
            }

            /* Next Steps */
            .english-next-steps {
                background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
                border-radius: 8px;
                padding: 20px;
                border-left: 4px solid #3b82f6;
            }

            .english-next-steps h4 {
                font-size: 1rem;
                font-weight: 600;
                color: #1e40af;
                margin: 0 0 12px 0;
            }

            .english-next-steps-text {
                font-size: 0.875rem;
                color: #1e40af;
                margin: 0;
                line-height: 1.6;
            }

            /* Error State */
            .english-error-state {
                text-align: center;
                padding: 40px 20px;
            }

            .english-error-icon {
                font-size: 3rem;
                margin-bottom: 16px;
            }

            .english-error-state h3 {
                font-size: 1.25rem;
                font-weight: 600;
                color: #111827;
                margin-bottom: 8px;
            }

            .english-error-state p {
                color: #6b7280;
                margin-bottom: 20px;
            }

            .english-retry-button {
                background: #3b82f6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                cursor: pointer;
                font-weight: 500;
                transition: background-color 0.2s ease;
            }

            .english-retry-button:hover {
                background: #2563eb;
            }

            /* Dark mode support */
            @media (prefers-color-scheme: dark) {
                .english-modal-content {
                    background: #1f2937;
                    color: #f9fafb;
                }

                .english-modal-header {
                    border-bottom-color: #374151;
                }

                .english-modal-title-container h2 {
                    color: #f9fafb;
                }

                .english-modal-title-container h3 {
                    color: #9ca3af;
                }

                .english-close-modal-button {
                    color: #9ca3af;
                }

                .english-close-modal-button:hover {
                    background-color: #374151;
                    color: #d1d5db;
                }

                .english-feedback-item {
                    background: #374151;
                    border-color: #4b5563;
                }

                .english-feedback-item h4 {
                    color: #f3f4f6;
                }

                .english-feedback-item p {
                    color: #d1d5db;
                }

                .english-overall-feedback {
                    background: #374151;
                }

                .english-overall-feedback h4 {
                    color: #f3f4f6;
                }

                .english-overall-feedback p {
                    color: #d1d5db;
                }

                .english-strengths-section,
                .english-improvements-section {
                    background: #374151;
                }

                .english-strengths-section h4,
                .english-improvements-section h4 {
                    color: #f9fafb;
                }

                .english-strengths-list li,
                .english-improvements-list li {
                    color: #d1d5db;
                }

                .english-next-steps h4 {
                    color: #93c5fd;
                }

                .english-next-steps-text {
                    color: #93c5fd;
                }

                .english-error-state h3 {
                    color: #f9fafb;
                }

                .english-error-state p {
                    color: #9ca3af;
                }
            }
        `;
        document.head.appendChild(styleSheet);
    }

})();
