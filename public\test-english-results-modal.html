<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>English Results Modal Test</title>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Montserrat', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .test-container {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 90%;
        }
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
            margin: 8px;
        }
        .test-button:hover {
            transform: translateY(-2px);
        }
        .scenario-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-2xl font-bold text-center mb-6">English Assessment Results Modal Test</h1>
        <p class="text-gray-600 text-center mb-8">Test the English assessment results modal with different scenarios</p>
        
        <div class="scenario-card">
            <h3 class="font-semibold mb-2">Scenario 1: Advanced Level (Qualified)</h3>
            <p class="text-sm text-gray-600 mb-3">Score: 18/21 - L2/GCSE Level - Ready for digital skills training</p>
            <button class="test-button" onclick="showTestModal('advanced')">Show Advanced Results</button>
        </div>

        <div class="scenario-card">
            <h3 class="font-semibold mb-2">Scenario 2: Intermediate Level</h3>
            <p class="text-sm text-gray-600 mb-3">Score: 12/21 - L1 Level - Some improvement needed</p>
            <button class="test-button" onclick="showTestModal('intermediate')">Show Intermediate Results</button>
        </div>

        <div class="scenario-card">
            <h3 class="font-semibold mb-2">Scenario 3: Foundation Level</h3>
            <p class="text-sm text-gray-600 mb-3">Score: 6/21 - Entry Level - Needs focused development</p>
            <button class="test-button" onclick="showTestModal('foundation')">Show Foundation Results</button>
        </div>

        <div class="scenario-card">
            <h3 class="font-semibold mb-2">Scenario 4: Enhanced Data</h3>
            <p class="text-sm text-gray-600 mb-3">Complete assessment with AI feedback and detailed analysis</p>
            <button class="test-button" onclick="showTestModal('enhanced')">Show Enhanced Results</button>
        </div>

        <div class="scenario-card">
            <h3 class="font-semibold mb-2">Scenario 5: Error State</h3>
            <p class="text-sm text-gray-600 mb-3">Test error handling when data is not available</p>
            <button class="test-button" onclick="showTestModal('error')">Show Error State</button>
        </div>
    </div>

    <!-- Mock Firebase for testing -->
    <script>
        // Mock Firebase db object for testing
        window.db = {
            collection: () => ({
                doc: () => ({
                    collection: () => ({
                        doc: () => ({
                            get: () => Promise.resolve({
                                exists: true,
                                data: () => getTestUserData()
                            })
                        })
                    })
                })
            })
        };

        // Mock loading overlay functions
        window.showLoadingOverlay = () => console.log('Loading...');
        window.hideLoadingOverlay = () => console.log('Loading complete');

        function getTestUserData() {
            const scenario = window.currentTestScenario || 'advanced';
            
            switch (scenario) {
                case 'advanced':
                    return {
                        englishAssessmentCompleted: true,
                        englishProficiencyScore: 18,
                        englishProficiencyLevel: 'L2/GCSE',
                        englishResponse: 'This is a sample response demonstrating advanced English proficiency with complex sentence structures and sophisticated vocabulary.',
                        englishAssessmentTimestamp: { toDate: () => new Date() },
                        timeSpentOnEnglish: 1800, // 30 minutes
                        englishFeedback: {
                            grammar: 'Excellent grammar skills demonstrated with complex sentence structures and accurate usage.',
                            vocabulary: 'Strong vocabulary range with appropriate word choice and professional terminology.',
                            coherence: 'Well-organized responses with clear logical flow and effective transitions.',
                            overall: 'Outstanding English proficiency at L2/GCSE level. Ready for advanced digital skills training.'
                        },
                        englishStrengths: [
                            'Excellent written communication skills',
                            'Strong grammar and sentence structure',
                            'Professional vocabulary usage',
                            'Clear and coherent expression',
                            'Ready for advanced learning'
                        ],
                        englishImprovements: [
                            'Continue practicing advanced writing techniques',
                            'Expand professional vocabulary',
                            'Maintain current proficiency level'
                        ]
                    };
                case 'intermediate':
                    return {
                        englishAssessmentCompleted: true,
                        englishProficiencyScore: 12,
                        englishProficiencyLevel: 'L1',
                        englishResponse: 'This response shows good basic English skills with some areas for improvement.',
                        englishAssessmentTimestamp: { toDate: () => new Date() },
                        timeSpentOnEnglish: 1200, // 20 minutes
                    };
                case 'foundation':
                    return {
                        englishAssessmentCompleted: true,
                        englishProficiencyScore: 6,
                        englishProficiencyLevel: 'Entry',
                        englishResponse: 'Basic response showing foundational English skills.',
                        englishAssessmentTimestamp: { toDate: () => new Date() },
                        timeSpentOnEnglish: 900, // 15 minutes
                    };
                case 'enhanced':
                    return {
                        englishAssessmentCompleted: true,
                        englishProficiencyScore: 15,
                        englishProficiencyLevel: 'L1',
                        englishResponse: 'A comprehensive response showcasing various English language skills and competencies.',
                        englishAssessmentTimestamp: { toDate: () => new Date() },
                        timeSpentOnEnglish: 2100, // 35 minutes
                        englishFeedback: {
                            grammar: 'Good grammar foundation with mostly accurate sentence construction. Some complex structures could be improved.',
                            vocabulary: 'Adequate vocabulary for everyday communication with room for expansion in professional terminology.',
                            coherence: 'Generally well-structured responses with clear main ideas. Transitions could be smoother.',
                            overall: 'Solid English proficiency at L1 level. With focused improvement, ready for digital skills training.'
                        },
                        englishStrengths: [
                            'Good basic communication skills',
                            'Adequate grammar foundation',
                            'Clear expression of main ideas',
                            'Willingness to engage with complex topics',
                            'Shows potential for improvement'
                        ],
                        englishImprovements: [
                            'Practice complex sentence structures',
                            'Expand vocabulary range',
                            'Work on coherence and organization',
                            'Improve transition phrases',
                            'Consider additional English language support'
                        ]
                    };
                default:
                    return null;
            }
        }

        // Function to show the test modal
        function showTestModal(scenario) {
            window.currentTestScenario = scenario;
            
            if (scenario === 'error') {
                // Test error state by passing null data
                if (typeof window.EnglishResultsModal !== 'undefined') {
                    window.EnglishResultsModal.show(null, '<EMAIL>', 'Test User', 'Test Company');
                } else {
                    alert('English Results Modal script not loaded properly');
                }
                return;
            }

            const testData = getTestUserData();
            if (typeof window.EnglishResultsModal !== 'undefined') {
                window.EnglishResultsModal.show(testData, '<EMAIL>', 'Test User', 'Test Company');
            } else {
                alert('English Results Modal script not loaded properly');
            }
        }
    </script>

    <!-- Include the English Results Modal script -->
    <script src="english-results-modal.js"></script>
</body>
</html>
